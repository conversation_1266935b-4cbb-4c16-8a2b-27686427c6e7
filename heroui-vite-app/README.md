# 柴管家前端管理系统

基于 HeroUI + Vite + React + TypeScript 构建的现代化管理系统界面，具有完整的响应式设计和优雅的用户体验。

## 🚀 项目启动步骤

### 环境要求

- **Node.js**: 16.0.0 或更高版本
- **包管理器**: npm（推荐）或 yarn、pnpm
- **浏览器**: 支持现代浏览器（Chrome、Firefox、Safari、Edge）

### 安装依赖

```bash
# 使用 npm（推荐）
npm install

# 或使用国内镜像源（推荐国内用户）
npm install --registry=https://registry.npmmirror.com

# 使用 yarn
yarn install

# 使用 pnpm
pnpm install
```

### 开发服务器

```bash
# 启动开发服务器（默认端口 5173）
cd heroui-vite-app && npm run dev

# 或者
yarn dev
pnpm dev
```

### 构建和预览

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 代码检查和自动修复
npm run lint
```

## 🔐 测试账号信息

### 登录验证机制
当前应用使用**模拟登录系统**，无需真实的账号验证：

- **任意邮箱格式**：输入任何符合邮箱格式的字符串（如：`<EMAIL>`）
- **任意密码**：输入任何非空密码（如：`password123`）
- **自动登录**：点击"Log In"按钮即可成功登录

### 推荐测试账号
```
邮箱：<EMAIL>
密码：password123
```

> **注意**：这是一个前端演示项目，登录功能仅用于界面展示，不涉及真实的用户认证。

## 🌐 项目访问信息

### 本地开发地址
- **主地址**: http://localhost:5173/
- **登录页面**: http://localhost:5173/login
- **注册页面**: http://localhost:5173/signup

### 主要页面路由

| 路由路径 | 页面名称 | 状态 | 说明 |
|---------|---------|------|------|
| `/login` | 登录页面 | ✅ 已完成 | 用户登录界面 |
| `/signup` | 注册页面 | ✅ 已完成 | 用户注册界面 |
| `/` 或 `/home` | 首页 | ✅ 已完成 | 项目概览和统计数据 |
| `/projects` | 项目管理 | ✅ 已完成 | 项目列表和管理功能 |
| `/tasks` | 任务管理 | 🚧 开发中 | 任务列表和跟踪 |
| `/team` | 团队管理 | 🚧 开发中 | 团队成员管理 |
| `/analytics` | 数据分析 | 🚧 开发中 | 数据统计和分析 |
| `/settings` | 系统设置 | 🚧 开发中 | 应用配置和设置 |

## 📱 功能说明

### 已实现功能

#### 🔐 认证系统
- **登录页面**：美观的登录界面，支持邮箱密码登录
- **注册页面**：用户注册界面，包含完整的表单验证
- **自动重定向**：未登录用户自动跳转到登录页面
- **状态持久化**：登录状态保存在 localStorage 中

#### 🏠 首页概览
- **统计卡片**：显示项目统计、任务完成率、团队成员等关键指标
- **最近活动**：展示最新的项目动态和操作记录
- **响应式布局**：支持桌面端和平板端的自适应显示

#### 📊 项目管理
- **项目列表**：以卡片形式展示所有项目
- **项目状态**：支持进行中、计划中、已完成等状态标识
- **进度跟踪**：可视化进度条显示项目完成情况
- **团队信息**：显示项目团队成员数量和截止日期
- **操作按钮**：查看详情和更多操作功能

#### 🎨 界面设计
- **侧边栏导航**：三层菜单结构，支持图标和状态指示
- **响应式设计**：桌面端固定侧边栏，平板端自适应
- **主题支持**：内置亮色和暗色主题切换
- **动画效果**：基于 Framer Motion 的流畅过渡动画

### 正在开发的功能

#### 📋 任务管理（开发中）
- 任务列表和详情页面
- 任务状态跟踪和更新
- 任务分配和优先级设置

#### 👥 团队管理（开发中）
- 团队成员列表和角色管理
- 权限控制和访问管理
- 团队协作功能

#### 📈 数据分析（开发中）
- 项目进度统计图表
- 团队效率分析报告
- 自定义数据仪表板

#### ⚙️ 系统设置（开发中）
- 用户个人资料设置
- 应用主题和偏好配置
- 通知和提醒设置

## 🛠️ 技术栈

### 核心框架
- **React 19.1.0** - 现代化 UI 框架
- **TypeScript** - 类型安全的 JavaScript
- **Vite 7.0.4** - 快速的构建工具
- **React Router DOM 7.7.1** - 客户端路由管理

### UI 组件库
- **HeroUI 2.8.2** - 现代化 UI 组件库
- **Tailwind CSS 4.1.11** - 实用优先的 CSS 框架
- **Framer Motion 12.23.12** - 强大的动画库
- **Iconify React 6.0.0** - 丰富的图标库

### 开发工具
- **ESLint** - 代码质量检查
- **TypeScript ESLint** - TypeScript 代码规范
- **PostCSS** - CSS 后处理器

## 🎯 项目特色

### ✨ 响应式设计
- **全屏适配**：完美占满浏览器窗口，无多余边距
- **断点优化**：针对桌面端和平板端进行专门优化
- **布局自适应**：内容区域根据屏幕尺寸自动调整列数

### 🚀 性能优化
- **按需加载**：组件和路由的懒加载
- **Tree Shaking**：自动移除未使用的代码
- **热重载**：开发时的快速更新体验

### 🎨 用户体验
- **现代化界面**：简洁美观的设计风格
- **流畅动画**：自然的页面过渡和交互反馈
- **直观导航**：清晰的信息架构和导航结构

## 📝 开发注意事项

### 代码规范
- 使用 TypeScript 进行类型检查
- 遵循 ESLint 代码规范
- 组件采用函数式组件 + Hooks 模式

### 样式开发
- 优先使用 Tailwind CSS 实用类
- 利用 HeroUI 主题系统
- 保持响应式设计原则

### 状态管理
- 使用 React Hooks 进行状态管理
- localStorage 用于持久化存储
- 组件间通过 props 传递数据

## 🔧 常见问题

### 端口占用问题
如果遇到端口 5173 被占用：
```bash
# 查找占用端口的进程
lsof -ti:5173

# 杀掉进程
kill $(lsof -ti:5173)

# 重新启动
npm run dev
```

### 依赖安装问题
如果遇到依赖安装失败：
```bash
# 清除缓存
npm cache clean --force

# 删除 node_modules 和 package-lock.json
rm -rf node_modules package-lock.json

# 重新安装
npm install
```

### 构建问题
确保所有 TypeScript 错误已修复：
```bash
# 检查 TypeScript 错误
npx tsc --noEmit

# 运行 ESLint 检查
npm run lint
```

## 📄 许可证

本项目仅用于学习和演示目的。

---

**最后更新**: 2025-01-30
**版本**: 1.0.0
**维护者**: 柴管家开发团队
