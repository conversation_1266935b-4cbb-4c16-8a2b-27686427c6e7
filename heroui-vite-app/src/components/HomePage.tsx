import React from 'react';
import { Card, CardBody, CardHeader } from '@heroui/react';

export default function HomePage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-foreground mb-2">欢迎来到首页</h1>
        <p className="text-default-500">这是一个使用HeroUI侧边栏模板的示例应用。</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">项目统计</h3>
          </CardHeader>
          <CardBody>
            <div className="text-3xl font-bold text-primary">12</div>
            <p className="text-default-500">活跃项目</p>
          </CardBody>
        </Card>
        
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">任务完成</h3>
          </CardHeader>
          <CardBody>
            <div className="text-3xl font-bold text-success">85%</div>
            <p className="text-default-500">本月完成率</p>
          </CardBody>
        </Card>
        
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">团队成员</h3>
          </CardHeader>
          <CardBody>
            <div className="text-3xl font-bold text-secondary">24</div>
            <p className="text-default-500">活跃成员</p>
          </CardBody>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">最近活动</h3>
        </CardHeader>
        <CardBody>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              <span className="text-sm">新项目"移动应用开发"已创建</span>
              <span className="text-xs text-default-400 ml-auto">2小时前</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-success rounded-full"></div>
              <span className="text-sm">任务"UI设计"已完成</span>
              <span className="text-xs text-default-400 ml-auto">4小时前</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-warning rounded-full"></div>
              <span className="text-sm">团队成员张三加入项目</span>
              <span className="text-xs text-default-400 ml-auto">1天前</span>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
