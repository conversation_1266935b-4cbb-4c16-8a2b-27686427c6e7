import {Chip} from "@heroui/react";
import {Icon} from "@iconify/react";

import {type SidebarItem} from "./sidebar";

/**
 * Please check the https://heroui.com/docs/guide/routing to have a seamless router integration
 */
const items: SidebarItem[] = [
  {
    key: "home",
    href: "#",
    icon: "solar:home-2-linear",
    title: "Home",
  },
  {
    key: "chat",
    href: "#",
    icon: "solar:chat-round-dots-linear",
    title: "Chat",
  },
  {
    key: "projects",
    href: "#",
    icon: "solar:widget-2-outline",
    title: "Projects",
    endContent: (
      <Icon className="text-default-500" icon="solar:add-circle-line-duotone" width={24} />
    ),
  },
  {
    key: "tasks",
    href: "#",
    icon: "solar:checklist-minimalistic-outline",
    title: "Tasks",
    endContent: (
      <Icon className="text-default-500" icon="solar:add-circle-line-duotone" width={24} />
    ),
  },
  {
    key: "team",
    href: "#",
    icon: "solar:users-group-two-rounded-outline",
    title: "Team",
  },
  {
    key: "tracker",
    href: "#",
    icon: "solar:sort-by-time-linear",
    title: "Tracker",
    endContent: (
      <Chip
        classNames={{
          base: "bg-foreground",
          content: "text-default-50",
        }}
        size="sm"
        variant="flat"
      >
        New
      </Chip>
    ),
  },
  {
    key: "analytics",
    href: "#",
    icon: "solar:chart-outline",
    title: "Analytics",
  },
  {
    key: "perks",
    href: "#",
    icon: "solar:gift-linear",
    title: "Perks",
    endContent: (
      <Chip
        classNames={{
          base: "bg-foreground",
          content: "text-default-50",
        }}
        size="sm"
        variant="flat"
      >
        3
      </Chip>
    ),
  },
  {
    key: "expenses",
    href: "#",
    icon: "solar:bill-list-outline",
    title: "Expenses",
  },
];

export default items;
