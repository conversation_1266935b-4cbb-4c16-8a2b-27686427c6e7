"use client";

import * as React from "react";
import { <PERSON><PERSON>, Card, CardBody, Divider, Input, Select, SelectItem, Spacer } from "@heroui/react";
import { Icon } from "@iconify/react";
import { cn } from "@heroui/react";
import { useTranslation } from "react-i18next";

import TeamManageTable from "./TeamManageTable";

interface TeamSettingCardProps {
  className?: string;
}



const TeamSetting = React.forwardRef<HTMLDivElement, TeamSettingCardProps>(
  ({ className, ...rest }, ref) => {
    const { t } = useTranslation('settings');

    const roleOptions = [
      { label: t('team.member'), value: "member", description: "team member" },
      { label: t('team.admin'), value: "admin", description: "team admin" },
      { label: t('team.owner'), value: "owner", description: "team owner" },
    ];

    return (
    <div {...rest} ref={ref} className={cn("p-2", className)}>
      {/* Title */}
      <p className="text-base font-medium text-default-700">{t('team.title')}</p>
      <p className="mt-1 text-sm font-normal text-default-400">{t('team.subtitle')}</p>
      {/* Invite */}
      <Card className="mt-4 bg-default-100" shadow="none">
        <CardBody className="px-4">
          <div className="flex items-start justify-between pb-3">
            <p className="mt-1.5 text-sm font-medium text-default-700">
              {t('team.invite_description')}
            </p>
            <Button
              className="bg-default-foreground text-background"
              endContent={<Icon className="h-3 w-3" icon="solar:link-linear" />}
              radius="md"
              size="sm"
            >
              {t('team.invite_link')}
            </Button>
          </div>
          <Divider />
          <Spacer y={3} />
          <div className="py-2">
            {/* Email Address */}
            <div className="flex items-center justify-between gap-3 ">
              <div className="flex-1">
                <p className="text-sm font-normal text-default-500">{t('team.email_address')}</p>
                <Input
                  className="mt-2"
                  classNames={{
                    inputWrapper: "bg-default-200",
                  }}
                  placeholder={t('team.email_placeholder')}
                />
              </div>
              <div className="flex-1">
                <p className="text-sm font-normal text-default-500">{t('team.role')}</p>
                <Select
                  className="mt-2"
                  classNames={{
                    trigger: "bg-default-200",
                  }}
                  defaultSelectedKeys={["member"]}
                >
                  {roleOptions.map((roleOption) => (
                    <SelectItem key={roleOption.value} value={roleOption.value}>
                      {roleOption.label}
                    </SelectItem>
                  ))}
                </Select>
              </div>
            </div>
            <Button
              className="mt-3 bg-default-200 text-default-700"
              endContent={<Icon className="h-[18px] w-[18px]" icon="solar:add-circle-linear" />}
              radius="md"
              size="sm"
            >
              {t('team.add_more')}
            </Button>
          </div>
          <Spacer y={3} />
          <Divider />
          <div>
            <div className="flex items-end justify-between pt-3">
              <p className="relative mb-2 text-xs text-default-500">
                {t('team.learn_more')} <span className="text-default-foreground">{t('team.team_members')}</span>
                <Icon
                  className={
                    "absolute right-0 top-0 h-2.5 w-2.5 translate-x-[8px] translate-y-[-2px] text-default-foreground"
                  }
                  icon="material-symbols-light:arrow-outward-rounded"
                />
              </p>
              <Button className="bg-default-foreground text-background" radius="md" size="sm">
                {t('team.send_invite')}
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>
      <Spacer y={4} />
      {/* Team management table */}
      <TeamManageTable />
    </div>
    );
  },
);

TeamSetting.displayName = "TeamSetting";

export default TeamSetting;
