"use client";

import * as React from "react";
import { Button, Input, RadioGroup, Select, SelectItem, Spacer } from "@heroui/react";
import { Icon } from "@iconify/react";
import { cn } from "@heroui/react";
import { useTranslation } from "react-i18next";

import { PlanCustomRadio } from "./PlanCustomRadio";

interface BillingSettingCardProps {
  className?: string;
}

const addressOptions = [
  {
    label: "Buenos Aires",
    value: "buenos-aires",
    description: "Buenos Aires",
  },
];

const countryOptions = [
  {
    label: "Argentina",
    value: "ar",
    description: "Argentina",
  },
];

const BillingSetting = React.forwardRef<HTMLDivElement, BillingSettingCardProps>(
  ({ className, ...props }, ref) => {
    const { t } = useTranslation('settings');

    return (
    <div ref={ref} className={cn("p-2", className)} {...props}>
      {/* Payment Method */}
      <div>
        <div className="rounded-large bg-default-100">
          <div className="flex items-center justify-between gap-2 px-4 py-3">
            <div className="flex items-center gap-3">
              <Icon className="h-6 w-6 text-default-500" icon="solar:card-outline" />
              <div>
                <p className="text-sm font-medium text-default-600">{t('billing.payment_method')}</p>
                <p className="text-xs text-default-400">{t('billing.payment_method_description')}</p>
              </div>
            </div>
            <Button
              className="bg-default-foreground text-background"
              radius="md"
              size="sm"
              variant="shadow"
            >
              {t('billing.update')}
            </Button>
          </div>
        </div>
      </div>
      <Spacer y={4} />
      {/* Current Plan */}
      <div>
        <p className="text-base font-medium text-default-700">{t('billing.current_plan')}</p>
        <p className="mt-1 text-sm font-normal text-default-400">
          {t('billing.trial_ends')} <span className="text-default-500">8 {t('billing.trial_days')}</span>
        </p>
        {/* Plan radio group */}
        <RadioGroup
          className="mt-4"
          classNames={{
            wrapper: "gap-4 flex-row flex-wrap",
          }}
          defaultValue="pro-monthly"
          orientation="horizontal"
        >
          <PlanCustomRadio
            classNames={{
              label: "text-default-500 font-medium",
            }}
            description={t('billing.pro_monthly')}
            value="pro-monthly"
          >
            <div className="mt-2">
              <p className="pt-2">
                <span className="text-[30px] font-semibold leading-7 text-default-foreground">
                  $12
                </span>
                &nbsp;<span className="text-xs font-medium text-default-400">{t('billing.price_monthly')}</span>
              </p>
              <ul className="list-inside list-disc text-xs font-normal text-default-500">
                <li>{t('billing.unlimited_users')}</li>
                <li>{t('billing.all_features')}</li>
                <li>{t('billing.email_chat_support')}</li>
                <li>{t('billing.billed_monthly')}</li>
              </ul>
            </div>
          </PlanCustomRadio>
          <PlanCustomRadio
            classNames={{
              label: "text-default-500 font-medium",
            }}
            description={t('billing.pro_yearly')}
            value="pro-yearly"
          >
            <div className="mt-2">
              <p className="pt-2">
                <span className="text-[30px] font-semibold leading-7 text-default-foreground">
                  $72
                </span>
                &nbsp;<span className="text-xs font-medium text-default-400">{t('billing.price_yearly')}</span>
              </p>
              <ul className="list-inside list-disc text-xs font-normal text-default-500">
                <li>{t('billing.unlimited_users')}</li>
                <li>{t('billing.all_features')}</li>
                <li>{t('billing.email_chat_support')}</li>
                <li>{t('billing.billed_monthly')}</li>
              </ul>
            </div>
          </PlanCustomRadio>
        </RadioGroup>
      </div>
      <Spacer y={4} />
      {/* Billing Address */}
      <div>
        {/*  Title */}
        <div>
          <p className="text-base font-medium text-default-700">{t('billing.billing_address')}</p>
          <p className="mt-1 text-sm font-normal text-default-400">
            {t('billing.billing_address_description')}
          </p>
        </div>
      </div>
      <div className="mt-2 space-y-2">
        <Input placeholder={t('billing.address_line_1')} />
        <Input placeholder={t('billing.address_line_2')} />
        <Input placeholder={t('billing.city')} />
        <div className="flex items-center gap-2">
          <Select defaultSelectedKeys={["buenos-aires"]}>
            {addressOptions.map((addressOption) => (
              <SelectItem key={addressOption.value} value={addressOption.value}>
                {addressOption.label}
              </SelectItem>
            ))}
          </Select>
          <Input placeholder={t('billing.postal_code')} />
        </div>
        <Select defaultSelectedKeys={["ar"]}>
          {countryOptions.map((countryOption) => (
            <SelectItem key={countryOption.value} value={countryOption.value}>
              {countryOption.label}
            </SelectItem>
          ))}
        </Select>
      </div>
      <Button className="mt-5 bg-default-foreground text-background" size="sm">
        {t('billing.save')}
      </Button>
    </div>
    );
  },
);

BillingSetting.displayName = "BillingSetting";

export default BillingSetting;
