"use client";

import * as React from "react";
import { RadioGroup, Select, SelectItem, Spacer } from "@heroui/react";
import { cn } from "@heroui/react";
import { useTranslation } from "react-i18next";

import { ThemeCustomRadio } from "./ThemeCustomRadio";
import SwitchCell from "./SwitchCell";

interface AppearanceSettingCardProps {
  className?: string;
}

const fontSizeOptions = [
  { label: "font_size_small", value: "small", description: "font size 14px" },
  { label: "font_size_medium", value: "medium", description: "font size 16px" },
  { label: "font_size_large", value: "large", description: "font size 18px" },
];

const AppearanceSetting = React.forwardRef<HTMLDivElement, AppearanceSettingCardProps>(
  ({ className, ...props }, ref) => {
    const { t } = useTranslation('settings');

    return (
      <div ref={ref} className={cn("p-2", className)} {...props}>
        {/* Theme */}
        <div>
          <p className="text-base font-medium text-default-700">{t('appearance.theme')}</p>
          <p className="mt-1 text-sm font-normal text-default-400">
            {t('appearance.theme_description')}
          </p>
        {/* Theme radio group */}
        <RadioGroup className="mt-4 flex-wrap" orientation="horizontal">
          <ThemeCustomRadio value="free" variant="light">
            {t('appearance.light')}
          </ThemeCustomRadio>
          <ThemeCustomRadio value="pro" variant="dark">
            {t('appearance.dark')}
          </ThemeCustomRadio>
        </RadioGroup>
      </div>
      <Spacer y={4} />
      {/* Font size */}
      <div className="flex items-start justify-between gap-2 py-2">
        <div>
          <p className="text-base font-medium text-default-700">{t('appearance.font_size')}</p>
          <p className="mt-1 text-sm font-normal text-default-400">{t('appearance.font_size_description')}</p>
        </div>
        <Select className="max-w-[200px]" defaultSelectedKeys={["large"]}>
          {fontSizeOptions.map((fontSizeOption) => (
            <SelectItem key={fontSizeOption.value} value={fontSizeOption.value}>
              {t(`appearance.${fontSizeOption.label}`)}
            </SelectItem>
          ))}
        </Select>
      </div>
      <Spacer y={4} />
      {/* Translucent UI */}
      <SwitchCell
        classNames={{
          base: "bg-transparent p-0",
        }}
        color="foreground"
        description={t('appearance.translucent_ui_description')}
        label={t('appearance.translucent_ui')}
      />
      <Spacer y={6} />
      {/* Use pointer cursor */}
      <SwitchCell
        classNames={{
          base: "bg-transparent p-0",
        }}
        color="foreground"
        description={t('appearance.pointer_cursor_description')}
        label={t('appearance.pointer_cursor')}
      />
      </div>
    );
  }
);

AppearanceSetting.displayName = "AppearanceSetting";

export default AppearanceSetting;
