const columns = [
  { name: "<PERSON><PERSON><PERSON>", uid: "name", sortable: true },
  { name: "<PERSON><PERSON><PERSON>", uid: "role", sortable: true },
  { name: "STATUS", uid: "status", sortable: true },
  { name: "ACTIONS", uid: "actions" },
];

const users = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Owner",
    team: "Management",
    status: "active",
    age: "29",
    avatar: "https://i.pravatar.cc/150?u=a042581f4e29026024d",
    email: "<EMAIL>",
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    role: "Member",
    team: "Development",
    status: "pending",
    age: "25",
    avatar: "https://i.pravatar.cc/150?u=a042581f4e29026704d",
    email: "<EMAIL>",
  },
  {
    id: 3,
    name: "<PERSON>",
    role: "Ad<PERSON>",
    team: "Development",
    status: "active",
    age: "22",
    avatar: "https://i.pravatar.cc/150?u=a04258114e29026702d",
    email: "<EMAIL>",
  },
  {
    id: 4,
    name: "<PERSON>",
    role: "Member",
    team: "Marketing",
    status: "vacation",
    age: "28",
    avatar: "https://i.pravatar.cc/150?u=a048581f4e29026701d",
    email: "<EMAIL>",
  },
];

// Simple unique function to replace lodash uniqWith
function uniqueBy<T>(array: T[], keyFn: (item: T) => any): T[] {
  const seen = new Set();
  return array.filter(item => {
    const key = keyFn(item);
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
}

const rolesOptions = uniqueBy(
  users.map((user) => ({
    name: user.role,
    uid: user.role.toLowerCase(),
  })),
  (item) => item.uid
);

const statusOptions = uniqueBy(
  users.map((user) => ({
    name: user.status,
    uid: user.status.toLowerCase(),
  })),
  (item) => item.uid
);

export { columns, users, rolesOptions, statusOptions };
