import React from 'react';
import { <PERSON>, Card<PERSON>ody, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@heroui/react';
import { Icon } from '@iconify/react';

export default function ProjectsPage() {
  const projects = [
    {
      id: 1,
      name: '移动应用开发',
      description: '开发一个跨平台的移动应用',
      status: 'active',
      progress: 75,
      team: 5,
      deadline: '2024-03-15'
    },
    {
      id: 2,
      name: '网站重构',
      description: '重构公司官网，提升用户体验',
      status: 'planning',
      progress: 25,
      team: 3,
      deadline: '2024-04-01'
    },
    {
      id: 3,
      name: 'API 开发',
      description: '开发新的 REST API 接口',
      status: 'completed',
      progress: 100,
      team: 4,
      deadline: '2024-02-28'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'primary';
      case 'planning': return 'warning';
      case 'completed': return 'success';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '进行中';
      case 'planning': return '计划中';
      case 'completed': return '已完成';
      default: return '未知';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-foreground mb-2">项目管理</h1>
          <p className="text-default-500">管理和跟踪所有项目进度。</p>
        </div>
        <Button 
          color="primary" 
          startContent={<Icon icon="solar:add-circle-line-duotone" width={20} />}
        >
          新建项目
        </Button>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {projects.map((project) => (
          <Card key={project.id} className="h-full">
            <CardHeader className="flex justify-between items-start">
              <div className="flex flex-col">
                <h3 className="text-lg font-semibold">{project.name}</h3>
                <p className="text-sm text-default-500 mt-1">{project.description}</p>
              </div>
              <Chip 
                color={getStatusColor(project.status) as any}
                size="sm"
                variant="flat"
              >
                {getStatusText(project.status)}
              </Chip>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>进度</span>
                    <span>{project.progress}%</span>
                  </div>
                  <div className="w-full bg-default-200 rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${project.progress}%` }}
                    ></div>
                  </div>
                </div>
                
                <div className="flex justify-between items-center text-sm">
                  <div className="flex items-center gap-1">
                    <Icon icon="solar:users-group-rounded-linear" width={16} />
                    <span>{project.team} 成员</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Icon icon="solar:calendar-linear" width={16} />
                    <span>{project.deadline}</span>
                  </div>
                </div>
                
                <div className="flex gap-2 pt-2">
                  <Button size="sm" variant="flat" className="flex-1">
                    查看详情
                  </Button>
                  <Button size="sm" variant="flat" isIconOnly>
                    <Icon icon="solar:menu-dots-linear" width={16} />
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>
    </div>
  );
}
