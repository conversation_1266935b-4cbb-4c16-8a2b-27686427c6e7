import React from "react";
import { Chip } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTranslation } from "react-i18next";
import { type SidebarItem } from "../components/sidebar";
import TeamAvatar from "../components/team-avatar";

export const useSidebarItems = () => {
  const { t } = useTranslation('common');

  const items: SidebarItem[] = [
    {
      key: "home",
      href: "#",
      icon: "solar:home-2-linear",
      title: t('navigation.home'),
    },
    {
      key: "projects",
      href: "#",
      icon: "solar:widget-2-outline",
      title: t('navigation.projects'),
      endContent: (
        <Icon className="text-default-400" icon="solar:add-circle-line-duotone" width={24} />
      ),
    },
    {
      key: "tasks",
      href: "#",
      icon: "solar:checklist-minimalistic-outline",
      title: t('navigation.tasks'),
      endContent: (
        <Icon className="text-default-400" icon="solar:add-circle-line-duotone" width={24} />
      ),
    },
    {
      key: "team",
      href: "#",
      icon: "solar:users-group-two-rounded-outline",
      title: t('navigation.team'),
    },
    {
      key: "tracker",
      href: "#",
      icon: "solar:sort-by-time-linear",
      title: t('navigation.tracker'),
      endContent: (
        <Chip size="sm" variant="flat">
          {t('common.new')}
        </Chip>
      ),
    },
    {
      key: "analytics",
      href: "#",
      icon: "solar:chart-outline",
      title: t('navigation.analytics'),
    },
    {
      key: "perks",
      href: "#",
      icon: "solar:gift-linear",
      title: t('navigation.perks'),
      endContent: (
        <Chip size="sm" variant="flat">
          3
        </Chip>
      ),
    },
    {
      key: "expenses",
      href: "#",
      icon: "solar:bill-list-outline",
      title: t('navigation.expenses'),
    },
    {
      key: "settings",
      href: "#",
      icon: "solar:settings-outline",
      title: t('navigation.settings'),
    },
  ];

  const sectionItems: SidebarItem[] = [
    {
      key: "overview",
      title: t('sidebar.overview'),
      items: [
        {
          key: "home",
          href: "/home",
          icon: "solar:home-2-linear",
          title: t('navigation.home'),
        },
        {
          key: "projects",
          href: "/projects",
          icon: "solar:widget-2-outline",
          title: t('navigation.projects'),
          endContent: (
            <Icon className="text-default-400" icon="solar:add-circle-line-duotone" width={24} />
          ),
        },
        {
          key: "tasks",
          href: "/tasks",
          icon: "solar:checklist-minimalistic-outline",
          title: t('navigation.tasks'),
          endContent: (
            <Icon className="text-default-400" icon="solar:add-circle-line-duotone" width={24} />
          ),
        },
        {
          key: "team",
          href: "/team",
          icon: "solar:users-group-two-rounded-outline",
          title: t('navigation.team'),
        },
        {
          key: "chat",
          href: "/messaging",
          icon: "solar:chat-round-line",
          title: t('navigation.chat'),
        },
        {
          key: "tracker",
          href: "#",
          icon: "solar:sort-by-time-linear",
          title: t('navigation.tracker'),
          endContent: (
            <Chip size="sm" variant="flat">
              {t('common.new')}
            </Chip>
          ),
        },
      ],
    },
    {
      key: "organization",
      title: t('sidebar.organization'),
      items: [
        {
          key: "cap_table",
          href: "#",
          title: t('navigation.cap_table'),
          icon: "solar:pie-chart-2-outline",
          items: [
            {
              key: "shareholders",
              href: "#",
              title: t('navigation.shareholders'),
            },
            {
              key: "note_holders",
              href: "#",
              title: t('navigation.note_holders'),
            },
            {
              key: "transactions_log",
              href: "#",
              title: t('navigation.transactions_log'),
            },
          ],
        },
        {
          key: "analytics",
          href: "/analytics",
          icon: "solar:chart-outline",
          title: t('navigation.analytics'),
        },
        {
          key: "perks",
          href: "/perks",
          icon: "solar:gift-linear",
          title: t('navigation.perks'),
          endContent: (
            <Chip size="sm" variant="flat">
              3
            </Chip>
          ),
        },
        {
          key: "expenses",
          href: "#",
          icon: "solar:bill-list-outline",
          title: t('navigation.expenses'),
        },
        {
          key: "settings",
          href: "/settings",
          icon: "solar:settings-outline",
          title: t('navigation.settings'),
        },
      ],
    },
  ];

  const sectionItemsWithTeams: SidebarItem[] = [
    ...sectionItems,
    {
      key: "your-teams",
      title: t('sidebar.your_teams'),
      items: [
        {
          key: "heroui",
          href: "#",
          title: "HeroUI",
          startContent: <TeamAvatar name="Hero UI" />,
        },
        {
          key: "tailwind-variants",
          href: "#",
          title: "Tailwind Variants",
          startContent: <TeamAvatar name="Tailwind Variants" />,
        },
        {
          key: "heroui-pro",
          href: "#",
          title: "HeroUI Pro",
          startContent: <TeamAvatar name="HeroUI Pro" />,
        },
      ],
    },
  ];

  return {
    items,
    sectionItems,
    sectionItemsWithTeams,
  };
};
