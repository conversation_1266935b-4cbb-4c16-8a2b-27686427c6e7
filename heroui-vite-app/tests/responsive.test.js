/**
 * 响应式设计测试用例
 * 测试应用在不同屏幕尺寸下的响应式行为
 */

// 常见的断点尺寸
const BREAKPOINTS = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  desktop: { width: 1440, height: 900 },
  largeDesktop: { width: 1920, height: 1080 }
};

describe('响应式设计测试', () => {
  
  test('移动端 - 侧边栏应该隐藏并显示汉堡菜单', async () => {
    // 设置移动端尺寸
    await page.setViewportSize(BREAKPOINTS.mobile);
    await page.goto('http://localhost:5173/');
    
    // 登录
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password');
    await page.click('button[type="submit"]');
    
    // 验证汉堡菜单按钮存在
    const hamburgerButton = await page.locator('button[aria-label*="menu"]').first();
    await expect(hamburgerButton).toBeVisible();
    
    // 验证侧边栏默认隐藏
    const sidebar = await page.locator('[role="dialog"]');
    await expect(sidebar).not.toBeVisible();
    
    // 点击汉堡菜单
    await hamburgerButton.click();
    
    // 验证侧边栏抽屉打开
    await expect(sidebar).toBeVisible();
  });

  test('平板端 - 侧边栏应该显示', async () => {
    await page.setViewportSize(BREAKPOINTS.tablet);
    await page.goto('http://localhost:5173/');
    
    // 登录
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password');
    await page.click('button[type="submit"]');
    
    // 验证侧边栏可见
    const sidebar = await page.locator('[role="listbox"]');
    await expect(sidebar).toBeVisible();
  });

  test('桌面端 - 侧边栏应该固定显示', async () => {
    await page.setViewportSize(BREAKPOINTS.desktop);
    await page.goto('http://localhost:5173/');
    
    // 登录
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password');
    await page.click('button[type="submit"]');
    
    // 验证侧边栏可见
    const sidebar = await page.locator('[role="listbox"]');
    await expect(sidebar).toBeVisible();
    
    // 验证汉堡菜单不存在
    const hamburgerButton = await page.locator('button[aria-label*="menu"]');
    await expect(hamburgerButton).not.toBeVisible();
  });

  test('首页网格布局响应式测试', async () => {
    // 测试不同屏幕尺寸下的网格布局
    for (const [size, dimensions] of Object.entries(BREAKPOINTS)) {
      await page.setViewportSize(dimensions);
      await page.goto('http://localhost:5173/');
      
      // 登录
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'password');
      await page.click('button[type="submit"]');
      
      // 验证统计卡片容器存在
      const statsGrid = await page.locator('.grid').first();
      await expect(statsGrid).toBeVisible();
      
      // 验证卡片数量
      const cards = await page.locator('.grid > div').count();
      expect(cards).toBeGreaterThan(0);
    }
  });

  test('项目页面网格布局响应式测试', async () => {
    await page.setViewportSize(BREAKPOINTS.desktop);
    await page.goto('http://localhost:5173/');
    
    // 登录
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password');
    await page.click('button[type="submit"]');
    
    // 导航到项目页面
    await page.click('[role="option"]:has-text("Projects")');
    
    // 测试不同屏幕尺寸
    for (const [size, dimensions] of Object.entries(BREAKPOINTS)) {
      await page.setViewportSize(dimensions);
      
      // 验证项目网格存在
      const projectGrid = await page.locator('.grid').last();
      await expect(projectGrid).toBeVisible();
      
      // 验证项目卡片存在
      const projectCards = await page.locator('.grid > div').count();
      expect(projectCards).toBeGreaterThan(0);
    }
  });

  test('登录页面响应式测试', async () => {
    for (const [size, dimensions] of Object.entries(BREAKPOINTS)) {
      await page.setViewportSize(dimensions);
      await page.goto('http://localhost:5173/login');
      
      // 验证登录表单可见
      const loginForm = await page.locator('form');
      await expect(loginForm).toBeVisible();
      
      // 验证输入框可见
      const emailInput = await page.locator('input[name="email"]');
      const passwordInput = await page.locator('input[name="password"]');
      await expect(emailInput).toBeVisible();
      await expect(passwordInput).toBeVisible();
      
      // 验证登录按钮可见
      const loginButton = await page.locator('button[type="submit"]');
      await expect(loginButton).toBeVisible();
    }
  });

  test('超大屏幕适配测试', async () => {
    // 测试超大屏幕（4K）
    await page.setViewportSize({ width: 3840, height: 2160 });
    await page.goto('http://localhost:5173/');
    
    // 登录
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password');
    await page.click('button[type="submit"]');
    
    // 验证布局不会过度拉伸
    const mainContent = await page.locator('main');
    await expect(mainContent).toBeVisible();
    
    // 验证侧边栏正常显示
    const sidebar = await page.locator('[role="listbox"]');
    await expect(sidebar).toBeVisible();
  });

});
