# HeroUI 使用指南

## 目录
1. [HeroUI 简介](#heroui-简介)
2. [核心特性](#核心特性)
3. [环境要求](#环境要求)
4. [安装配置](#安装配置)
5. [Tailwind CSS v4 集成](#tailwind-css-v4-集成)
6. [路由系统配置](#路由系统配置)
7. [常用组件示例](#常用组件示例)
8. [最佳实践](#最佳实践)
9. [常见问题解答](#常见问题解答)

## HeroUI 简介

HeroUI（原 NextUI）是一个基于 React 的现代化 UI 组件库，专为构建美观、快速且可访问的用户界面而设计。它建立在 [Tailwind CSS](https://tailwindcss.com/) 和 [React Aria](https://react-spectrum.adobe.com/react-aria/) 的基础之上，提供了完整的组件系统。

### 设计理念
- **美观性**：提供现代化的设计风格和精美的视觉效果
- **可访问性**：基于 React Aria 确保所有组件都符合 WCAG 标准
- **可定制性**：基于 Tailwind CSS 提供灵活的样式定制能力
- **开发效率**：简化开发流程，提供开箱即用的组件

## 核心特性

- ✅ **50+ 高质量组件**：涵盖常见的 UI 需求
- ✅ **TypeScript 支持**：完整的类型定义
- ✅ **主题系统**：支持亮色/暗色主题切换
- ✅ **响应式设计**：适配各种屏幕尺寸
- ✅ **无障碍访问**：符合 WCAG 2.1 标准
- ✅ **Tree Shaking**：按需加载，优化包体积
- ✅ **服务端渲染**：支持 Next.js、Remix 等框架
- ✅ **动画效果**：基于 Framer Motion 的流畅动画

## 环境要求

在开始使用 HeroUI 之前，请确保您的开发环境满足以下要求：

- **React**: 18.0.0 或更高版本
- **Tailwind CSS**: v4.0.0 或更高版本
- **Framer Motion**: 11.9.0 或更高版本
- **Node.js**: 16.0.0 或更高版本

## 安装配置

### 方法一：自动安装（推荐）

使用 HeroUI CLI 是最简单的安装方式：

```bash
# 安装 CLI
npm install -g @heroui/cli

# 初始化项目
heroui init my-heroui-app

# 进入项目目录
cd my-heroui-app

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 方法二：手动安装

#### 1. 全局安装

```bash
# 使用 npm
npm install @heroui/react framer-motion

# 使用 pnpm
pnpm add @heroui/react framer-motion

# 使用 yarn
yarn add @heroui/react framer-motion
```

#### 2. 配置 Tailwind CSS

创建 `hero.ts` 文件：

```typescript
// hero.ts
import { heroui } from "@heroui/react";

export default heroui();
```

更新主 CSS 文件：

```css
/* styles/globals.css */
@import "tailwindcss";
@plugin "@heroui/theme";
```

#### 3. 配置 Provider

在应用根组件中添加 `HeroUIProvider`：

```tsx
// app/layout.tsx (Next.js App Router)
import { HeroUIProvider } from '@heroui/react'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        <HeroUIProvider>
          {children}
        </HeroUIProvider>
      </body>
    </html>
  )
}
```

### 方法三：按需安装

如果您只需要特定组件，可以单独安装：

```bash
# 安装核心包
npm install @heroui/system @heroui/theme

# 安装特定组件（以 Button 为例）
npm install @heroui/button
```

## Tailwind CSS v4 集成

HeroUI v2.8.0+ 支持 Tailwind CSS v4。以下是迁移步骤：

### 1. 升级 HeroUI

```bash
npm install @heroui/react@latest
```

### 2. 更新 CSS 导入

#### 不使用 tailwind.config.js（推荐）

创建 `hero.ts` 文件：

```typescript
// hero.ts
import { heroui } from "@heroui/react";

export default heroui();
```

更新主 CSS 文件：

```css
/* styles/globals.css */
@import "tailwindcss";
@plugin "@heroui/theme";
```

#### 使用 tailwind.config.js（向后兼容）

```javascript
// tailwind.config.js
import { heroui } from "@heroui/react";

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
    // 其他内容路径...
  ],
  theme: {
    extend: {},
  },
  darkMode: "class",
  plugins: [heroui()],
};
```

### 3. 更新 PostCSS 配置

```javascript
// postcss.config.js
module.exports = {
  plugins: {
    '@tailwindcss/postcss': {},
    // 注意：autoprefixer 不再需要
  },
}
```

### 4. 更新 Vite 配置（仅 Vite 项目）

```javascript
// vite.config.js
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  css: {
    postcss: './postcss.config.js',
  },
})
```

## 路由系统配置

HeroUI 组件支持与各种路由库集成，实现客户端导航。

### 基本配置

在 `HeroUIProvider` 中配置路由：

```tsx
import { HeroUIProvider } from '@heroui/react'

function App() {
  const navigate = (href: string) => {
    // 您的路由导航逻辑
  }

  return (
    <HeroUIProvider navigate={navigate}>
      {/* 您的应用内容 */}
    </HeroUIProvider>
  )
}
```

### Next.js 集成

#### App Router

```tsx
// app/providers.tsx
'use client'

import { HeroUIProvider } from '@heroui/react'
import { useRouter } from 'next/navigation'

export function Providers({ children }: { children: React.ReactNode }) {
  const router = useRouter()

  return (
    <HeroUIProvider navigate={router.push}>
      {children}
    </HeroUIProvider>
  )
}
```

```tsx
// app/layout.tsx
import { Providers } from './providers'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  )
}
```

#### Pages Router

```tsx
// pages/_app.tsx
import { HeroUIProvider } from '@heroui/react'
import { useRouter } from 'next/router'

export default function App({ Component, pageProps }) {
  const router = useRouter()

  return (
    <HeroUIProvider navigate={router.push}>
      <Component {...pageProps} />
    </HeroUIProvider>
  )
}
```

### React Router 集成

```tsx
import { HeroUIProvider } from '@heroui/react'
import { useNavigate, useHref } from 'react-router-dom'

function App() {
  const navigate = useNavigate()
  const useHrefHook = useHref

  return (
    <HeroUIProvider navigate={navigate} useHref={useHrefHook}>
      {/* 您的路由配置 */}
    </HeroUIProvider>
  )
}
```

## 常用组件示例

### Button 组件

```tsx
import { Button } from "@heroui/react";

export default function ButtonExample() {
  return (
    <div className="flex gap-4">
      {/* 基础按钮 */}
      <Button>默认按钮</Button>
      
      {/* 不同颜色 */}
      <Button color="primary">主要按钮</Button>
      <Button color="secondary">次要按钮</Button>
      <Button color="success">成功按钮</Button>
      <Button color="warning">警告按钮</Button>
      <Button color="danger">危险按钮</Button>
      
      {/* 不同变体 */}
      <Button variant="solid">实心</Button>
      <Button variant="bordered">边框</Button>
      <Button variant="light">轻量</Button>
      <Button variant="flat">扁平</Button>
      <Button variant="faded">褪色</Button>
      <Button variant="shadow">阴影</Button>
      <Button variant="ghost">幽灵</Button>
      
      {/* 不同尺寸 */}
      <Button size="sm">小按钮</Button>
      <Button size="md">中按钮</Button>
      <Button size="lg">大按钮</Button>
      
      {/* 加载状态 */}
      <Button isLoading>加载中</Button>
      
      {/* 禁用状态 */}
      <Button isDisabled>禁用按钮</Button>
      
      {/* 带图标 */}
      <Button startContent={<span>📧</span>}>
        发送邮件
      </Button>
    </div>
  );
}
```

### Input 组件

```tsx
import { Input } from "@heroui/react";

export default function InputExample() {
  return (
    <div className="flex flex-col gap-4 max-w-md">
      {/* 基础输入框 */}
      <Input label="用户名" placeholder="请输入用户名" />

      {/* 不同变体 */}
      <Input variant="flat" label="扁平样式" placeholder="扁平输入框" />
      <Input variant="bordered" label="边框样式" placeholder="边框输入框" />
      <Input variant="underlined" label="下划线样式" placeholder="下划线输入框" />
      <Input variant="faded" label="褪色样式" placeholder="褪色输入框" />

      {/* 不同尺寸 */}
      <Input size="sm" label="小尺寸" placeholder="小输入框" />
      <Input size="md" label="中尺寸" placeholder="中输入框" />
      <Input size="lg" label="大尺寸" placeholder="大输入框" />

      {/* 不同颜色 */}
      <Input color="primary" label="主要颜色" placeholder="主要输入框" />
      <Input color="secondary" label="次要颜色" placeholder="次要输入框" />
      <Input color="success" label="成功颜色" placeholder="成功输入框" />
      <Input color="warning" label="警告颜色" placeholder="警告输入框" />
      <Input color="danger" label="危险颜色" placeholder="危险输入框" />

      {/* 带图标 */}
      <Input
        label="邮箱"
        placeholder="请输入邮箱"
        startContent={<span>📧</span>}
      />

      {/* 密码输入 */}
      <Input
        label="密码"
        placeholder="请输入密码"
        type="password"
        endContent={<span>👁️</span>}
      />

      {/* 验证状态 */}
      <Input
        label="邮箱验证"
        placeholder="请输入邮箱"
        isInvalid
        errorMessage="请输入有效的邮箱地址"
      />
    </div>
  );
}
```

### Card 组件

```tsx
import { Card, CardHeader, CardBody, CardFooter, Button, Image } from "@heroui/react";

export default function CardExample() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {/* 基础卡片 */}
      <Card>
        <CardHeader>
          <h4 className="font-bold text-large">基础卡片</h4>
        </CardHeader>
        <CardBody>
          <p>这是一个基础的卡片组件示例。</p>
        </CardBody>
        <CardFooter>
          <Button>了解更多</Button>
        </CardFooter>
      </Card>

      {/* 带图片的卡片 */}
      <Card>
        <CardHeader className="pb-0 pt-2 px-4 flex-col items-start">
          <p className="text-tiny uppercase font-bold">每日照片</p>
          <small className="text-default-500">12 个轨道可用</small>
          <h4 className="font-bold text-large">前端电台</h4>
        </CardHeader>
        <CardBody className="overflow-visible py-2">
          <Image
            alt="Card background"
            className="object-cover rounded-xl"
            src="/api/placeholder/270/200"
            width={270}
          />
        </CardBody>
      </Card>

      {/* 可点击的卡片 */}
      <Card isPressable onPress={() => console.log("卡片被点击")}>
        <CardHeader>
          <h4 className="font-bold text-large">可点击卡片</h4>
        </CardHeader>
        <CardBody>
          <p>点击这个卡片试试看！</p>
        </CardBody>
      </Card>
    </div>
  );
}
```

### Modal 组件

```tsx
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Button,
  useDisclosure
} from "@heroui/react";

export default function ModalExample() {
  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  return (
    <>
      <Button onPress={onOpen}>打开模态框</Button>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                模态框标题
              </ModalHeader>
              <ModalBody>
                <p>这是模态框的内容区域。</p>
                <p>您可以在这里放置任何内容。</p>
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  取消
                </Button>
                <Button color="primary" onPress={onClose}>
                  确认
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
```

### Navbar 组件

```tsx
import {
  Navbar,
  NavbarBrand,
  NavbarContent,
  NavbarItem,
  NavbarMenuToggle,
  NavbarMenu,
  NavbarMenuItem,
  Link,
  Button
} from "@heroui/react";
import { useState } from "react";

export default function NavbarExample() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const menuItems = [
    "首页",
    "关于",
    "服务",
    "联系",
  ];

  return (
    <Navbar onMenuOpenChange={setIsMenuOpen}>
      <NavbarContent>
        <NavbarMenuToggle
          aria-label={isMenuOpen ? "关闭菜单" : "打开菜单"}
          className="sm:hidden"
        />
        <NavbarBrand>
          <p className="font-bold text-inherit">ACME</p>
        </NavbarBrand>
      </NavbarContent>

      <NavbarContent className="hidden sm:flex gap-4" justify="center">
        <NavbarItem>
          <Link color="foreground" href="#">
            首页
          </Link>
        </NavbarItem>
        <NavbarItem isActive>
          <Link href="#" aria-current="page">
            关于
          </Link>
        </NavbarItem>
        <NavbarItem>
          <Link color="foreground" href="#">
            服务
          </Link>
        </NavbarItem>
      </NavbarContent>

      <NavbarContent justify="end">
        <NavbarItem className="hidden lg:flex">
          <Link href="#">登录</Link>
        </NavbarItem>
        <NavbarItem>
          <Button as={Link} color="primary" href="#" variant="flat">
            注册
          </Button>
        </NavbarItem>
      </NavbarContent>

      <NavbarMenu>
        {menuItems.map((item, index) => (
          <NavbarMenuItem key={`${item}-${index}`}>
            <Link
              color={
                index === 2 ? "primary" : index === menuItems.length - 1 ? "danger" : "foreground"
              }
              className="w-full"
              href="#"
              size="lg"
            >
              {item}
            </Link>
          </NavbarMenuItem>
        ))}
      </NavbarMenu>
    </Navbar>
  );
}
```

## 最佳实践

### 1. 主题配置

创建自定义主题：

```typescript
// theme.ts
import { heroui } from "@heroui/react";

const customTheme = heroui({
  themes: {
    light: {
      colors: {
        primary: {
          50: "#eff6ff",
          100: "#dbeafe",
          500: "#3b82f6",
          900: "#1e3a8a",
          DEFAULT: "#3b82f6",
        },
      },
    },
    dark: {
      colors: {
        primary: {
          50: "#1e3a8a",
          100: "#1e40af",
          500: "#3b82f6",
          900: "#eff6ff",
          DEFAULT: "#3b82f6",
        },
      },
    },
  },
});

export default customTheme;
```

### 2. 响应式设计

```tsx
import { Button } from "@heroui/react";

export default function ResponsiveExample() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      <Button className="w-full sm:w-auto">
        响应式按钮
      </Button>
    </div>
  );
}
```

### 3. 性能优化

```tsx
// 使用按需导入
import { Button } from "@heroui/button";
import { Input } from "@heroui/input";

// 而不是
// import { Button, Input } from "@heroui/react";
```

### 4. 类型安全

```typescript
import { ButtonProps } from "@heroui/react";

interface CustomButtonProps extends ButtonProps {
  customProp?: string;
}

const CustomButton: React.FC<CustomButtonProps> = ({ customProp, ...props }) => {
  return <Button {...props} />;
};
```

### 5. 样式覆盖

```tsx
import { Button } from "@heroui/react";

export default function CustomStyledButton() {
  return (
    <Button
      className="bg-gradient-to-r from-pink-500 to-violet-500 text-white"
      variant="solid"
    >
      自定义样式按钮
    </Button>
  );
}
```

## 常见问题解答

### Q: HeroUI 与其他 UI 库有什么区别？

**A:** HeroUI 的主要优势包括：
- **完整的可访问性支持**：基于 React Aria，确保所有组件都符合 WCAG 标准
- **现代化设计**：提供美观的默认样式和流畅的动画效果
- **TypeScript 优先**：完整的类型定义和类型安全
- **Tailwind CSS 集成**：无缝集成，支持自定义样式
- **Tree Shaking**：按需加载，优化包体积

### Q: 如何自定义组件样式？

**A:** HeroUI 提供多种自定义方式：

1. **使用 className 属性**：
```tsx
<Button className="bg-red-500 hover:bg-red-600">
  自定义按钮
</Button>
```

2. **使用主题系统**：
```typescript
const customTheme = heroui({
  themes: {
    light: {
      colors: {
        primary: "#your-color",
      },
    },
  },
});
```

3. **使用 CSS 变量**：
```css
.custom-button {
  --heroui-primary: #your-color;
}
```

### Q: HeroUI 支持服务端渲染吗？

**A:** 是的，HeroUI 完全支持服务端渲染（SSR），兼容：
- Next.js (App Router 和 Pages Router)
- Remix
- Gatsby
- 其他支持 SSR 的 React 框架

### Q: 如何处理暗色主题？

**A:** HeroUI 内置暗色主题支持：

```tsx
import { HeroUIProvider } from "@heroui/react";

function App() {
  return (
    <HeroUIProvider>
      <div className="dark"> {/* 添加 dark 类 */}
        {/* 您的应用内容 */}
      </div>
    </HeroUIProvider>
  );
}
```

### Q: 如何优化包体积？

**A:** 推荐的优化方法：

1. **按需导入组件**：
```tsx
import { Button } from "@heroui/button";
// 而不是 import { Button } from "@heroui/react";
```

2. **配置 Tree Shaking**：
```javascript
// next.config.js
module.exports = {
  transpilePackages: ["@heroui/react"],
};
```

3. **使用生产构建**：
```bash
npm run build
```

### Q: 如何处理表单验证？

**A:** HeroUI 组件支持多种验证方式：

```tsx
import { Input, Button } from "@heroui/react";
import { useForm } from "react-hook-form";

export default function FormExample() {
  const { register, handleSubmit, formState: { errors } } = useForm();

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Input
        {...register("email", { required: "邮箱是必填项" })}
        label="邮箱"
        isInvalid={!!errors.email}
        errorMessage={errors.email?.message}
      />
      <Button type="submit">提交</Button>
    </form>
  );
}
```

### Q: 如何集成图标库？

**A:** HeroUI 可以与任何图标库集成：

```tsx
import { Button } from "@heroui/react";
import { IconMail } from "@tabler/icons-react";

export default function IconExample() {
  return (
    <Button startContent={<IconMail size={16} />}>
      发送邮件
    </Button>
  );
}
```

### Q: 如何处理国际化？

**A:** HeroUI 支持国际化，可以与 react-i18next 等库集成：

```tsx
import { HeroUIProvider } from "@heroui/react";
import { useTranslation } from "react-i18next";

function App() {
  const { t } = useTranslation();

  return (
    <HeroUIProvider locale="zh-CN">
      {/* 您的应用内容 */}
    </HeroUIProvider>
  );
}
```

### Q: 遇到问题如何获取帮助？

**A:** 您可以通过以下渠道获取帮助：
- **GitHub Issues**：[https://github.com/heroui-inc/heroui/issues](https://github.com/heroui-inc/heroui/issues)
- **Discord 社区**：[https://discord.gg/9b6yyZKmH4](https://discord.gg/9b6yyZKmH4)
- **官方文档**：[https://www.heroui.com/docs](https://www.heroui.com/docs)
- **Stack Overflow**：使用 `heroui` 标签

---

## 总结

HeroUI 是一个功能强大、易于使用的 React UI 组件库，它结合了现代化的设计、完整的可访问性支持和灵活的定制能力。通过本指南，您应该能够：

1. ✅ 理解 HeroUI 的核心概念和优势
2. ✅ 成功安装和配置 HeroUI
3. ✅ 集成 Tailwind CSS v4
4. ✅ 配置路由系统
5. ✅ 使用常见组件构建界面
6. ✅ 应用最佳实践优化项目
7. ✅ 解决常见问题

开始您的 HeroUI 之旅，构建美观、可访问且高性能的 React 应用程序！

---

*最后更新：2025年1月*
*版本：HeroUI v2.8.0+*
```
