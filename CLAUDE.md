# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目结构

这是一个基于 HeroUI + Vite + React + TypeScript 的前端项目，包含多个子项目：

- `heroui-vite-app/` - 主要的 Vite 应用程序，包含完整的管理系统界面
- `heroui/` - HeroUI 组件示例和模板集合
- 文档文件 - HeroUI 使用指南和踩坑指南

## 常用开发命令

### 主应用开发 (heroui-vite-app)
```bash
cd heroui-vite-app

# 启动开发服务器 (默认端口5173)
npm run dev

# 构建生产版本 (包含 TypeScript 编译)
npm run build

# 代码检查和自动修复
npm run lint

# 预览构建结果
npm run preview
```

### 依赖管理
```bash
# 安装依赖
npm install

# 使用国内镜像安装（推荐）
npm install --registry=https://registry.npmmirror.com
```

### TypeScript 编译
```bash
# TypeScript 增量编译 (包含在 build 中)
npx tsc -b
```

## 技术栈

### 核心框架
- **React 19.1.0** - UI 框架
- **TypeScript** - 类型系统
- **Vite** - 构建工具
- **React Router DOM** - 路由管理

### UI 组件库
- **HeroUI 2.8.2** - 主要 UI 组件库
- **Tailwind CSS 4.1.11** - 样式框架
- **Framer Motion** - 动画库
- **Iconify React** - 图标库

### 开发工具
- **ESLint** - 代码检查
- **PostCSS** - CSS 处理
- **TypeScript ESLint** - TypeScript 代码检查

## 应用架构

### 路由结构
应用使用 React Router 实现单页面应用导航：

- `/login` - 登录页面
- `/signup` - 注册页面  
- `/` 或 `/home` - 首页
- `/projects` - 项目管理页面
- `/tasks` - 任务管理页面（开发中）
- `/team` - 团队管理页面（开发中）
- `/analytics` - 数据分析页面（开发中）
- `/settings` - 设置页面（开发中）

### 认证系统
应用实现了基于 localStorage 的简单认证系统：
- 未认证用户自动重定向到登录页面
- 认证状态存储在 `localStorage.isAuthenticated`
- 认证后显示侧边栏布局的主应用界面

### 组件架构
- **布局组件**:
  - `SidebarLayout` - 主布局组件，包含侧边栏导航和顶部头部
  - `Sidebar` - 侧边栏导航组件，支持多级菜单和响应式
- **认证组件**:
  - `LoginForm`/`SignUpForm` - 认证相关表单组件
- **页面组件**:
  - `HomePage` - 主页面，包含项目概览和快速操作
  - `ProjectsPage` - 项目管理页面，显示项目列表和状态
- **配置文件**:
  - `sidebar-items.tsx` - 侧边栏菜单配置，包含完整的菜单结构定义
  - `types.ts` - TypeScript 类型定义，定义 SVG 图标属性等

### 侧边栏导航系统
侧边栏使用 HeroUI 的 Sidebar 组件，具有以下特性：
- **三层菜单结构**: Overview（主要功能）、Organization（组织管理）、Your Teams（团队协作）
- **动态路由集成**: 基于 React Router 自动高亮当前页面
- **图标系统**: 使用 Iconify React 的 Solar 图标集
- **状态指示**: 支持徽章、芯片等状态指示器
- **响应式布局**: 桌面端固定侧边栏，小屏幕抽屉模式
- **搜索功能**: 内置搜索和用户信息显示

## 开发注意事项

### HeroUI 集成
- 使用 Tailwind CSS v4 新语法：`@import "tailwindcss"` 和 `@plugin "@heroui/theme"`
- 组件需要 `"use client"` 指令（如果迁移到 Next.js）
- 所有 HeroUI 组件都包含在主入口 `@heroui/react` 中

### 样式开发
- 使用 Tailwind CSS 实用类进行样式开发
- HeroUI 主题系统提供设计令牌
- 支持响应式设计断点
- 内置暗色主题支持

### TypeScript 配置
- **严格模式**: 启用所有严格检查选项
- **编译目标**: ES2022，支持现代浏览器特性
- **模块解析**: Bundler 模式，与 Vite 集成
- **类型检查**: 未使用变量和参数检查，无穿透 case 检查
- **组件类型**: 在 `types.ts` 中定义，包含 SVG 属性接口

### 开发工作流
1. 使用 `npm run dev` 启动开发服务器
2. 代码更改会自动热重载
3. 使用 `npm run lint` 检查代码质量
4. 构建前运行 `npm run build` 验证生产版本

## 常见问题解决

### 端口占用问题
如果遇到端口占用，先杀掉占用进程：
```bash
# 查找占用端口的进程
lsof -ti:5173

# 杀掉进程
kill $(lsof -ti:5173)

# 然后重新启动
npm run dev
```

### HeroUI 组件问题
- 确保所有组件都正确导入自 `@heroui/react`
- 检查 Tailwind CSS 配置是否包含 HeroUI 主题
- 参考项目中的 `HeroUI-NextJS-踩坑指南.md` 和 `heroui使用指南.md`

### 构建问题
- 确保所有依赖都已正确安装
- 检查 TypeScript 错误
- 验证 ESLint 规则通过

## 代码架构说明

### ESLint 配置
项目使用现代 ESLint 配置（`eslint.config.js`）：
- **扁平配置**: 基于 TypeScript ESLint v8+ 的新配置格式
- **React 集成**: 包含 React Hooks 和 React Refresh 插件
- **严格规则**: 使用推荐配置集确保代码质量
- **全局忽略**: 自动忽略 `dist` 构建目录

### 菜单系统设计
`sidebar-items.tsx` 导出三个菜单配置：
- **items**: 简单菜单项数组，用于基础侧边栏
- **sectionItems**: 分组菜单，包含 Overview 和 Organization 两个部分
- **sectionItemsWithTeams**: 完整菜单，添加 Your Teams 团队部分

每个菜单项支持：
- 图标（Iconify）、标题、链接
- 结束内容（徽章、按钮等）
- 嵌套子菜单（多级结构）
- 团队头像（TeamAvatar 组件）

### 认证流程设计
- **状态管理**: 使用 React useState + localStorage 持久化
- **路由保护**: 未认证时重定向到 `/login`
- **布局切换**: 认证前显示登录表单，认证后显示侧边栏布局
- **登出功能**: 清除 localStorage 并重置认证状态

## 推荐的开发实践

1. **组件开发**: 优先使用 HeroUI 组件，需要自定义时使用 Tailwind CSS
2. **类型安全**: 充分利用 TypeScript 类型系统，为新组件定义接口
3. **代码质量**: 遵循 ESLint 规则，保持代码一致性
4. **响应式设计**: 使用 Tailwind 响应式类，确保浏览器窗口适应
5. **图标使用**: 统一使用 Iconify React 的 Solar 图标集