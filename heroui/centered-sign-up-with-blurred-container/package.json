{"dependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "react-scripts": "^4.0.0", "clsx": "2.1.0", "tailwind-merge": "2.2.0", "framer-motion": "11.1.7", "@iconify/react": "4.1.1", "@heroui/react": "latest"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^4.0.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.4.3"}, "main": "/index.tsx"}