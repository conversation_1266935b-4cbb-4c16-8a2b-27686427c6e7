import {isEqual, uniqWith} from "lodash";

const columns = [
  {name: "<PERSON>AM<PERSON>", uid: "name", sortable: true},
  {name: "<PERSON><PERSON><PERSON>", uid: "role", sortable: true},
  {name: "STAT<PERSON>", uid: "status", sortable: true},
  {name: "ACTIONS", uid: "actions"},
];

const users = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Owner",
    team: "Management",
    status: "active",
    age: "29",
    avatar: "https://i.pravatar.cc/150?u=a042581f4e29026024d",
    email: "<EMAIL>",
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    role: "Member",
    team: "Development",
    status: "pending",
    age: "25",
    avatar: "https://i.pravatar.cc/150?u=a042581f4e29026704d",
    email: "<EMAIL>",
  },
];

/**
 * To use this function you need to install lodash in your project
 * ```bash
 * npm install lodash
 * ```
 */
const rolesOptions = uniqWith(
  users.map((user) => {
    return {
      name: user.role,
      uid: user.role.toLowerCase(),
    };
  }),
  isEqual,
);

/**
 * To use this function you need to install lodash in your project
 * ```bash
 * npm install lodash
 * ```
 */
const statusOptions = uniqWith(
  users.map((user) => {
    return {
      name: user.status,
      uid: user.status.toLowerCase(),
    };
  }),
  isEqual,
);

export {columns, users, rolesOptions, statusOptions};
